"use client";

import { useState, useEffect } from "react";
import { use<PERSON>out<PERSON> } from "next/navigation";
import { useParams } from 'react-router-dom';
import { formatDate } from "@/utils/date-utils";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Heading } from "@/components/ui/heading";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Upload,
  Edit,
  Users,
  CalendarDays,
  Copy,
  CheckCircle,
  UserRound,
  CalendarPlus,
  Save,
  Clock,
  Info
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import {
  fetchChannel,
  updateChannel,
  updateChannelThumbnail,
  updateChannelBanner
} from "@/services/channel-service";
import { fetchChannelEvents } from "@/services/event-service";
import { uploadImage, uploadDocument } from "@/services/file-service";
import { Channel, Event } from "@/interfaces/types";
import { isAuthenticated } from "@/lib/auth";

// Country list for dropdown
const COUNTRIES = [
  { code: "US", name: "United States" },
  { code: "GB", name: "United Kingdom" },
  { code: "CA", name: "Canada" },
  { code: "AU", name: "Australia" },
  { code: "DE", name: "Germany" },
  { code: "FR", name: "France" },
  { code: "JP", name: "Japan" },
  { code: "CN", name: "China" },
  { code: "IN", name: "India" },
  { code: "BR", name: "Brazil" },
  { code: "ZA", name: "South Africa" },
  { code: "NG", name: "Nigeria" },
  { code: "ZW", name: "Zimbabwe" },
];

// Currency list
const CURRENCIES = [
  { code: "USD", name: "US Dollar" },
  { code: "EUR", name: "Euro" },
  { code: "GBP", name: "British Pound" },
  { code: "JPY", name: "Japanese Yen" },
  { code: "CAD", name: "Canadian Dollar" },
  { code: "AUD", name: "Australian Dollar" },
];

export default function ChannelViewPage({
  isEditing: initialIsEditing = false,
  channelId: propChannelId
}: {
  isEditing?: boolean;
  channelId?: string;
}) {
  const router = useRouter();
  const params = useParams();
  const { toast } = useToast();

  // Get the ID from props first, then fall back to URL parameters
  const id = propChannelId || (params.id as string);

  const [channel, setChannel] = useState<Channel | null>(null);
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [thumbnailFile, setThumbnailFile] = useState<File | null>(null);
  const [bannerFile, setBannerFile] = useState<File | null>(null);
  const [thumbnailPreview, setThumbnailPreview] = useState<string | null>(null);
  const [bannerPreview, setBannerPreview] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isEditing, setIsEditing] = useState(initialIsEditing);

  // Form state for editable fields
  const [channelName, setChannelName] = useState<string>('');
  const [channelDescription, setChannelDescription] = useState<string>('');
  const [channelCurrency, setChannelCurrency] = useState<string>('');
  const [channelCountry, setChannelCountry] = useState<string>('');

  // Document state for verification
  const [identityDocument, setIdentityDocument] = useState<File | null>(null);
  const [companyDocuments, setCompanyDocuments] = useState<FileList | null>(null);
  const [addressDocument, setAddressDocument] = useState<File | null>(null);
  const [otherDocuments, setOtherDocuments] = useState<FileList | null>(null);
  const [previousEvents, setPreviousEvents] = useState<string>('');
  const [documentUrls, setDocumentUrls] = useState<{
    identityUrl?: string;
    companyUrls?: string[];
    addressUrl?: string;
    otherUrls?: string[];
  }>({});

  useEffect(() => {
    const loadChannelData = async () => {
      try {
        setLoading(true);

        if (!isAuthenticated()) {
          router.push("/login");
          return;
        }

        // Load channel details
        const channelData = await fetchChannel(id);
        console.log('Initial channel data:', channelData);
        setChannel(channelData);

        // If the channel is in pending_approval status and we're initially in editing mode, exit editing mode
        if (channelData.status === 'pending_approval' && initialIsEditing) {
          setIsEditing(false);
        }

        // Initialize form state with channel data
        setChannelName(channelData.name || '');
        setChannelDescription(channelData.description || '');
        setChannelCurrency(channelData.currency || '');
        setChannelCountry(channelData.countryIso2 || '');

        // Set preview images if available
        if (channelData.imageUrl) {
          setThumbnailPreview(channelData.imageUrl);
        }

        if (channelData.bannerUrl) {
          setBannerPreview(channelData.bannerUrl);
        }

        // Set document URLs if available
        if (channelData.documents) {
          const docs = channelData.documents;
          console.log('Received documents:', docs);
          setDocumentUrls({
            identityUrl: docs.identityUrl,
            companyUrls: docs.companyUrls || [],
            addressUrl: docs.addressUrl,
            otherUrls: docs.otherUrls || []
          });
          setPreviousEvents(channelData.previousEvents || '');
        }

        // Load channel events
        const eventsData = await fetchChannelEvents(id);
        setEvents(eventsData);
      } catch (err) {
        console.error('Failed to load channel data', err);
        setError('Failed to load channel data');
        toast({
          title: "Error",
          description: "Failed to load channel data",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    loadChannelData();
  }, [id, router]);

  const handleThumbnailUpdate = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files?.length || !channel) return;

    const file = e.target.files[0];
    setThumbnailFile(file);

    // Create preview
    const reader = new FileReader();
    reader.onload = (event) => {
      if (event.target?.result) {
        setThumbnailPreview(event.target.result as string);
      }
    };
    reader.readAsDataURL(file);

    // Update channel with new thumbnail
    try {
      setIsUpdating(true);

      // First upload the image using the file service
      const fileResponse = await uploadImage(file);

      // Then update the channel with the image URL using the dedicated endpoint
      const updatedChannel = await updateChannelThumbnail(channel.id, fileResponse.url);

      // Update the local state
      setChannel(updatedChannel);

      toast({
        title: "Success",
        description: "Thumbnail updated successfully",
      });
    } catch (err) {
      console.error('Failed to update thumbnail', err);
      toast({
        title: "Error",
        description: "Failed to update thumbnail",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleBannerUpdate = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files?.length || !channel) return;

    const file = e.target.files[0];
    setBannerFile(file);

    // Create preview
    const reader = new FileReader();
    reader.onload = (event) => {
      if (event.target?.result) {
        setBannerPreview(event.target.result as string);
      }
    };
    reader.readAsDataURL(file);

    // Update channel with new banner
    try {
      setIsUpdating(true);

      // First upload the image using the file service
      const fileResponse = await uploadImage(file);

      // Then update the channel with the image URL using the dedicated endpoint
      const updatedChannel = await updateChannelBanner(channel.id, fileResponse.url);

      // Update the local state
      setChannel(updatedChannel);

      toast({
        title: "Success",
        description: "Banner updated successfully",
      });
    } catch (err) {
      console.error('Failed to update banner', err);
      toast({
        title: "Error",
        description: "Failed to update banner",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleUpdateChannelDetails = async () => {
    if (!channel) return;

    try {
      setIsUpdating(true);

      // Prepare the update data
      const updateData = {
        name: channelName,
        description: channelDescription,
        currency: channelCurrency,
        countryIso2: channelCountry
      };

      // Update the channel
      const updatedChannel = await updateChannel(channel.id, updateData);

      // Update the local state
      setChannel(updatedChannel);

      // Exit editing mode after successful update
      setIsEditing(false);

      toast({
        title: "Success",
        description: "Channel details updated successfully",
      });
    } catch (err) {
      console.error('Failed to update channel details', err);
      toast({
        title: "Error",
        description: "Failed to update channel details",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleCancelEdit = () => {
    // Reset form values to original channel data
    if (channel) {
      setChannelName(channel.name || '');
      setChannelDescription(channel.description || '');
      setChannelCurrency(channel.currency || '');
      setChannelCountry(channel.countryIso2 || '');
    }
    // Exit editing mode
    setIsEditing(false);
  };

  const copyToClipboard = (text: string, message: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied",
      description: message,
    });
  };

  const handleDocumentChange = async (field: string, e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files?.length || !channel) return;

    // Set the file in state based on the field
    if (field === 'identity') {
      setIdentityDocument(e.target.files[0]);
    } else if (field === 'company') {
      setCompanyDocuments(e.target.files);
    } else if (field === 'address') {
      setAddressDocument(e.target.files[0]);
    } else if (field === 'other') {
      setOtherDocuments(e.target.files);
    }
  };

  const handlePreviousEventsChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setPreviousEvents(e.target.value);
  };

  const handleVerificationSubmit = async (isDraft: boolean = false) => {
    if (!channel) return;

    // Don't allow document submission for active channels
    if (channel.status === 'active') {
      return;
    }

    try {
      setIsUpdating(true);
      const updatedDocumentUrls = { ...documentUrls };
      let hasRequiredDocuments = false;

      // Upload identity document (required)
      if (identityDocument) {
        try {
          const response = await uploadDocument(identityDocument);
          updatedDocumentUrls.identityUrl = response.url;
          hasRequiredDocuments = true;
        } catch (error) {
          console.error('Failed to upload identity document:', error);
          toast({
            title: "Error",
            description: "Failed to upload identity document",
            variant: "destructive",
          });
          setIsUpdating(false);
          return;
        }
      } else if (documentUrls.identityUrl) {
        // If we already have an identity document URL, consider it valid
        hasRequiredDocuments = true;
      }

      // Upload company documents (required)
      if (companyDocuments && companyDocuments.length > 0) {
        const companyUrls: string[] = [];
        for (let i = 0; i < companyDocuments.length; i++) {
          try {
            const response = await uploadDocument(companyDocuments[i]);
            companyUrls.push(response.url);
          } catch (error) {
            console.error(`Failed to upload company document ${i + 1}:`, error);
            toast({
              title: "Error",
              description: `Failed to upload company document ${i + 1}`,
              variant: "destructive",
            });
            setIsUpdating(false);
            return;
          }
        }
        if (companyUrls.length > 0) {
          updatedDocumentUrls.companyUrls = [
            ...(updatedDocumentUrls.companyUrls || []),
            ...companyUrls
          ];
        }
      }

      // Upload address document (required)
      if (addressDocument) {
        try {
          const response = await uploadDocument(addressDocument);
          updatedDocumentUrls.addressUrl = response.url;
        } catch (error) {
          console.error('Failed to upload address document:', error);
          toast({
            title: "Error",
            description: "Failed to upload address document",
            variant: "destructive",
          });
          setIsUpdating(false);
          return;
        }
      }

      // Upload other documents (optional)
      if (otherDocuments && otherDocuments.length > 0) {
        const otherUrls: string[] = [];
        for (let i = 0; i < otherDocuments.length; i++) {
          try {
            const response = await uploadDocument(otherDocuments[i]);
            otherUrls.push(response.url);
          } catch (error) {
            console.error(`Failed to upload other document ${i + 1}:`, error);
            toast({
              title: "Error",
              description: `Failed to upload other document ${i + 1}`,
              variant: "destructive",
            });
          }
        }
        if (otherUrls.length > 0) {
          updatedDocumentUrls.otherUrls = [
            ...(updatedDocumentUrls.otherUrls || []),
            ...otherUrls
          ];
        }
      }

      // Check if we have all required channel details and documents
      const missingItems = [];

      // Validate channel details
      if (!channel.imageUrl || channel.imageUrl.trim() === '') {
        missingItems.push("Channel thumbnail");
      }

      if (!channel.bannerUrl || channel.bannerUrl.trim() === '') {
        missingItems.push("Channel banner");
      }

      if (!channel.name || channel.name.trim() === '') {
        missingItems.push("Channel name");
      }

      if (!channel.description || channel.description.trim() === '') {
        missingItems.push("Channel description");
      }

      if (!channel.countryIso2 || channel.countryIso2.trim() === '') {
        missingItems.push("Country");
      }

      if (!channel.currency || channel.currency.trim() === '') {
        missingItems.push("Currency");
      }

      // Validate verification documents
      if (!hasRequiredDocuments) {
        missingItems.push("Identity/Passport document");
      }

      if (!updatedDocumentUrls.companyUrls || updatedDocumentUrls.companyUrls.length === 0) {
        missingItems.push("Company documents");
      }

      if (!updatedDocumentUrls.addressUrl) {
        missingItems.push("Proof of address document");
      }

      if (!previousEvents || previousEvents.trim() === '') {
        missingItems.push("Previous event links");
      }

      if (missingItems.length > 0) {
        const itemsList = missingItems.join(', ');
        toast({
          title: "Missing Information",
          description: `The following required information is missing: ${itemsList}`,
          variant: "destructive",
        });
        setIsUpdating(false);
        return;
      }

      // Update the channel with the document URLs and previous events
      const updateData = {
        documents: updatedDocumentUrls,
        previousEvents: previousEvents,
        // Status logic:
        // - If it's a draft and status is 'pending', set to 'draft'
        // - If it's a draft and status is something else, keep the current status
        // - If it's a final submission, always set to 'pending_approval'
        status: isDraft
          ? (channel.status === 'pending' ? 'draft' : channel.status)
          : 'pending_approval'
      };

      console.log(`Sending document data (${isDraft ? 'draft' : 'final submission'}):`, JSON.stringify(updateData));

      // Update the channel
      const updatedChannel = await updateChannel(channel.id, updateData);

      console.log('Response from server:', updatedChannel);
      console.log('Previous status:', channel.status, 'New status:', updatedChannel.status);

      // Update the local state with the response from the server
      const updatedChannelWithStatus = {
        ...updatedChannel,
        status: updatedChannel.status || (isDraft ? 'draft' : 'pending_approval') // Ensure status is set correctly
      };
      console.log('Setting channel state to:', updatedChannelWithStatus);
      setChannel(updatedChannelWithStatus);
      setDocumentUrls(updatedDocumentUrls);

      // Show appropriate success message based on the action
      if (isDraft) {
        toast({
          title: "Success",
          description: "Documents saved as draft successfully",
        });
      } else if (channel.status === 'pending_approval' && isEditing) {
        toast({
          title: "Success",
          description: "Documents updated and resubmitted for approval successfully",
        });
        // Exit editing mode after successful submission
        setIsEditing(false);
      } else {
        toast({
          title: "Success",
          description: "Verification documents submitted for approval successfully",
        });
        // Exit editing mode after successful submission
        setIsEditing(false);
      }
    } catch (err) {
      console.error('Failed to submit verification documents', err);
      // Show appropriate error message based on the action
      if (isDraft) {
        toast({
          title: "Error",
          description: "Failed to save documents as draft",
          variant: "destructive",
        });
      } else if (channel.status === 'pending_approval' && isEditing) {
        toast({
          title: "Error",
          description: "Failed to update and resubmit documents",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Error",
          description: "Failed to submit verification documents",
          variant: "destructive",
        });
      }
    } finally {
      setIsUpdating(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'rejected':
        return <Badge variant="destructive">Rejected</Badge>;
      case 'draft':
        return <Badge variant="outline" className="bg-blue-500/10 text-blue-600">Draft</Badge>;
      case 'pending_approval':
        return <Badge variant="outline" className="bg-amber-500/10 text-amber-600">Pending Approval</Badge>;
      default:
        return <Badge variant="outline" className="bg-yellow-500/10 text-yellow-600">Pending</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="container py-8">
        <div className="flex justify-center items-center h-60">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error || !channel) {
    return (
      <div className="container py-8">
        <div className="text-center py-12 border rounded-lg">
          <h2 className="text-2xl font-bold mb-4">Channel not found</h2>
          <p className="text-red-500 mb-4">{error}</p>
          <Button onClick={() => router.push('/account/channels')}>Back to Channels</Button>
        </div>
      </div>
    );
  }

  const thumbnailUrl = thumbnailPreview || channel.imageUrl || '/placeholder.svg';
  const bannerUrl = bannerPreview || channel.bannerUrl || '/placeholder.svg';

  console.log('Rendering with channel status:', channel.status, 'isEditing:', isEditing);

  return (
    <div className="container py-8">
      <Button
        variant="ghost"
        className="mb-6 flex items-center gap-2"
        onClick={() => router.push('/account/channels')}
      >
        <ArrowLeft className="h-4 w-4" />
        Back to Channels
      </Button>

      <div className="relative mb-6">
        <div className="h-48 md:h-64 w-full rounded-lg overflow-hidden bg-muted">
          <img
            src={bannerUrl}
            alt={`${channel.name} banner`}
            className="w-full h-full object-cover"
          />

          <div className="absolute bottom-4 right-4">
            <Label htmlFor="banner-upload" className="cursor-pointer">
              <div className="bg-black/50 text-white p-2 rounded-md flex items-center gap-2 hover:bg-black/70 transition-colors">
                <Edit className="h-4 w-4" />
                <span>Update Banner</span>
              </div>
              <Input
                id="banner-upload"
                type="file"
                className="hidden"
                accept="image/*"
                onChange={handleBannerUpdate}
                disabled={isUpdating}
              />
            </Label>
          </div>
        </div>

        <div className="absolute -bottom-12 left-4 flex items-end">
          <div className="relative">
            <div className="h-24 w-24 rounded-md overflow-hidden border-4 border-background bg-muted">
              <img
                src={thumbnailUrl}
                alt={channel.name}
                className="w-full h-full object-cover"
              />
            </div>

            <Label htmlFor="thumbnail-upload" className="absolute bottom-0 right-0 cursor-pointer">
              <div className="bg-black/50 text-white p-1.5 rounded-full hover:bg-black/70 transition-colors">
                <Edit className="h-3.5 w-3.5" />
              </div>
              <Input
                id="thumbnail-upload"
                type="file"
                className="hidden"
                accept="image/*"
                onChange={handleThumbnailUpdate}
                disabled={isUpdating}
              />
            </Label>
          </div>

          <div className="ml-4 pb-2">
            <div className="flex items-center gap-2">
              <h1 className="text-2xl font-bold">{channel.name}</h1>
              {getStatusBadge(channel.status)}
            </div>
            <p className="text-sm text-muted-foreground">{channel.handle}</p>
          </div>
        </div>
      </div>

      <div className="mt-16">
        <Tabs defaultValue="details">
          <TabsList className="mb-4">
            <TabsTrigger value="details">Channel Details</TabsTrigger>
            <TabsTrigger value="events">Events</TabsTrigger>
            <TabsTrigger value="verification">Verification</TabsTrigger>
            {channel.status === 'active' && (
              <TabsTrigger value="stream">Stream Settings</TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="details">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Channel Information</CardTitle>
                  <CardDescription>
                    Basic information about your channel. All fields marked with * are required for verification.
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  {isEditing ? (
                    <>
                      <Button
                        variant="outline"
                        onClick={handleCancelEdit}
                        disabled={isUpdating}
                        className="flex items-center gap-2"
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={handleUpdateChannelDetails}
                        disabled={isUpdating}
                        className="flex items-center gap-2"
                      >
                        {isUpdating ? (
                          <>
                            <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full"></div>
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="h-4 w-4" />
                            Save Changes
                          </>
                        )}
                      </Button>
                    </>
                  ) : (
                    <Button
                      variant="outline"
                      onClick={() => setIsEditing(true)}
                      className="flex items-center gap-2"
                    >
                      <Edit className="h-4 w-4" />
                      Edit Details
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="channel-name">Channel Name <span className="text-red-500">*</span></Label>
                    <Input
                      id="channel-name"
                      value={isEditing ? channelName : channel.name}
                      onChange={(e) => setChannelName(e.target.value)}
                      readOnly={!isEditing}
                    />
                  </div>
                  <div>
                    <Label htmlFor="channel-handle">Handle</Label>
                    <Input id="channel-handle" value={channel.handle} readOnly />
                    <p className="text-xs text-muted-foreground mt-1">Handle cannot be changed</p>
                  </div>
                </div>

                <div>
                  <Label htmlFor="channel-description">Description <span className="text-red-500">*</span></Label>
                  <Textarea
                    id="channel-description"
                    value={isEditing ? channelDescription : channel.description}
                    onChange={(e) => setChannelDescription(e.target.value)}
                    readOnly={!isEditing}
                  />
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="channel-country">Country <span className="text-red-500">*</span></Label>
                    {isEditing ? (
                      <Select
                        value={channelCountry}
                        onValueChange={setChannelCountry}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select country" />
                        </SelectTrigger>
                        <SelectContent>
                          {COUNTRIES.map((country) => (
                            <SelectItem key={country.code} value={country.code}>
                              {country.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    ) : (
                      <Input
                        id="channel-country"
                        value={channel.countryIso2}
                        readOnly
                      />
                    )}
                  </div>
                  <div>
                    <Label htmlFor="channel-currency">Currency <span className="text-red-500">*</span></Label>
                    {isEditing ? (
                      <Select
                        value={channelCurrency}
                        onValueChange={setChannelCurrency}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select currency" />
                        </SelectTrigger>
                        <SelectContent>
                          {CURRENCIES.map((currency) => (
                            <SelectItem key={currency.code} value={currency.code}>
                              {currency.code} - {currency.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    ) : (
                      <Input
                        id="channel-currency"
                        value={channel.currency}
                        readOnly
                      />
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="events">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Channel Events</CardTitle>
                  <CardDescription>Manage your channel's events</CardDescription>
                </div>
                <Button
                  onClick={() => router.push(`/event-create?channelId=${channel.id}`)}
                  className="flex items-center gap-2"
                  disabled={channel.status !== 'active'}
                >
                  <CalendarPlus className="h-4 w-4" />
                  Create Event
                </Button>
              </CardHeader>
              <CardContent>
                {events.length === 0 ? (
                  <div className="text-center py-8">
                    <h3 className="font-medium mb-2">No Events Created Yet</h3>
                    <p className="text-muted-foreground mb-4">
                      {channel.status === 'active'
                        ? 'Create your first event to start streaming'
                        : 'Your channel needs to be approved before you can create events'}
                    </p>
                    {channel.status === 'active' && (
                      <Button onClick={() => router.push(`/event-create?channelId=${channel.id}`)}>
                        Create Event
                      </Button>
                    )}
                  </div>
                ) : (
                  <div className="relative">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Title</TableHead>
                          <TableHead>Date</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Price</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {events.map((event) => (
                          <TableRow key={event.id}>
                            <TableCell>{event.title}</TableCell>
                            <TableCell>
                              {formatDate(event.startTime, 'PPP')}
                            </TableCell>
                            <TableCell>
                              <Badge
                                variant={event.status === 'live' ? 'default' : 'secondary'}
                              >
                                {event.status}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              {event.price} {event.currency}
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => router.push(`/event/${event.id}`)}
                                >
                                  View
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => router.push(`/event/edit/${event.id}`)}
                                >
                                  Edit
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="verification">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Channel Verification</CardTitle>
                  <CardDescription>
                    {channel.status === 'active' && "Your channel has been approved."}
                    {channel.status === 'pending' && "Submit required documents to verify your channel."}
                    {channel.status === 'draft' && "Your documents are saved as draft. Complete your submission when ready."}
                    {channel.status === 'pending_approval' && "Your verification is pending approval from our team."}
                  </CardDescription>
                </div>
                {isEditing && channel.status !== 'active' && (
                  <Button
                    onClick={() => handleVerificationSubmit(true)}
                    disabled={isUpdating}
                    className="flex items-center gap-2"
                    variant="outline"
                  >
                    {isUpdating ? (
                      <>
                        <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full"></div>
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4" />
                        Save Draft
                      </>
                    )}
                  </Button>
                )}
              </CardHeader>
              <CardContent>
                {channel.status === 'active' ? (
                  <div className="flex items-center justify-center p-8 text-center">
                    <div>
                      <div className="flex justify-center">
                        <CheckCircle className="h-16 w-16 text-green-500 mb-4" />
                      </div>
                      <h3 className="text-xl font-medium mb-2">Channel Approved</h3>
                      <p className="text-muted-foreground mb-4">
                        Your channel has been verified and approved by our team.
                        You can now create and host events.
                      </p>
                      <Button onClick={() => router.push(`/event-create?channelId=${channel.id}`)}>
                        Create an Event
                      </Button>
                    </div>
                  </div>
                ) : channel.status === 'pending_approval' && !isEditing ? (
                  <div className="flex items-center justify-center p-8 text-center">
                    <div className="w-full max-w-md">
                      <div className="flex justify-center">
                        <Clock className="h-16 w-16 text-amber-500 mb-4" />
                      </div>
                      <h3 className="text-xl font-medium mb-2">Verification Pending</h3>
                      <div className="bg-amber-50 border border-amber-200 rounded-md p-4 mb-6">
                        <p className="text-amber-800 mb-2">
                          Your verification documents have been submitted and are pending review by our team.
                        </p>
                        <p className="text-amber-700 text-sm">
                          This process typically takes 1-2 business days. You'll be notified once your channel is approved.
                        </p>
                      </div>
                      <div className="flex gap-2 justify-center">
                        <Button
                          variant="outline"
                          onClick={() => setIsEditing(true)}
                          className="flex items-center gap-2"
                        >
                          <Edit className="h-4 w-4" />
                          Edit Documents
                        </Button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {/* Requirements notice */}
                    <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                      <div className="flex items-start">
                        <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
                        <div>
                          <h4 className="font-medium text-blue-800 mb-1">Verification Requirements</h4>
                          <p className="text-blue-700 text-sm mb-2">
                            Before submitting for verification, ensure you have completed all required information:
                          </p>
                          <ul className="text-blue-700 text-sm list-disc list-inside space-y-1">
                            <li>Channel thumbnail and banner images</li>
                            <li>Complete channel details (name, description, country, currency)</li>
                            <li>All required verification documents</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    {isEditing && channel.status === 'pending_approval' && (
                      <div className="bg-amber-50 border border-amber-200 rounded-md p-4 mb-6">
                        <div className="flex items-start">
                          <Info className="h-5 w-5 text-amber-600 mt-0.5 mr-2 flex-shrink-0" />
                          <div>
                            <h4 className="font-medium text-amber-800 mb-1">Editing Documents for Pending Verification</h4>
                            <p className="text-amber-700 text-sm">
                              You're editing documents that have already been submitted for verification.
                              Any changes will require resubmission and may reset your place in the verification queue.
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                    <div className="space-y-2">
                      <Label>Identity/Passport <span className="text-red-500">*</span></Label>
                      {documentUrls.identityUrl && (
                        <div className="flex items-center gap-2 mb-2 text-sm text-green-600">
                          <CheckCircle className="h-4 w-4" />
                          Document uploaded successfully
                        </div>
                      )}
                      <Input
                        type="file"
                        onChange={(e) => handleDocumentChange('identity', e)}
                        disabled={isUpdating}
                      />
                      <p className="text-xs text-muted-foreground">
                        Required. Upload a clear scan or photo of your ID or passport.
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label>Company Documents <span className="text-red-500">*</span></Label>
                      {documentUrls.companyUrls && documentUrls.companyUrls.length > 0 && (
                        <div className="flex items-center gap-2 mb-2 text-sm text-green-600">
                          <CheckCircle className="h-4 w-4" />
                          {documentUrls.companyUrls.length} document(s) uploaded
                        </div>
                      )}
                      <Input
                        type="file"
                        multiple
                        onChange={(e) => handleDocumentChange('company', e)}
                        disabled={isUpdating}
                      />
                      <p className="text-xs text-muted-foreground">
                        Required. Upload business registration, certificates, or licenses.
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label>Proof of Address <span className="text-red-500">*</span></Label>
                      {documentUrls.addressUrl && (
                        <div className="flex items-center gap-2 mb-2 text-sm text-green-600">
                          <CheckCircle className="h-4 w-4" />
                          Document uploaded successfully
                        </div>
                      )}
                      <Input
                        type="file"
                        onChange={(e) => handleDocumentChange('address', e)}
                        disabled={isUpdating}
                      />
                      <p className="text-xs text-muted-foreground">
                        Required. Upload a utility bill, bank statement, or other proof of address.
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label>Other Documents</Label>
                      {documentUrls.otherUrls && documentUrls.otherUrls.length > 0 && (
                        <div className="flex items-center gap-2 mb-2 text-sm text-green-600">
                          <CheckCircle className="h-4 w-4" />
                          {documentUrls.otherUrls.length} document(s) uploaded
                        </div>
                      )}
                      <Input
                        type="file"
                        multiple
                        onChange={(e) => handleDocumentChange('other', e)}
                        disabled={isUpdating}
                      />
                      <p className="text-xs text-muted-foreground">
                        Optional. Upload any other relevant documents.
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label>Previous Event Links <span className="text-red-500">*</span></Label>
                      <Textarea
                        placeholder="Enter links to your previous events on other platforms (one per line)"
                        className="min-h-[100px]"
                        value={previousEvents}
                        onChange={handlePreviousEventsChange}
                        disabled={isUpdating}
                      />
                      <p className="text-xs text-muted-foreground">
                        Required. Provide links to your best events on other platforms. These will be used to rate your channel.
                      </p>
                    </div>

                    <div className="flex gap-2">
                      {isEditing && channel.status === 'pending_approval' && (
                        <Button
                          variant="outline"
                          onClick={() => setIsEditing(false)}
                          disabled={isUpdating}
                          className="flex-1"
                        >
                          Cancel
                        </Button>
                      )}
                      <Button
                        onClick={() => handleVerificationSubmit(false)}
                        disabled={isUpdating}
                        className={isEditing && channel.status === 'pending_approval' ? "flex-1" : "w-full"}
                      >
                        {isUpdating ? (
                          <>
                            <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full mr-2"></div>
                            Submitting...
                          </>
                        ) : (
                          isEditing && channel.status === 'pending_approval'
                            ? "Update and Resubmit Documents"
                            : isEditing
                              ? "Save and Submit for Verification"
                              : "Submit for Verification"
                        )}
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {channel.status === 'active' && (
            <TabsContent value="stream">
              <Card>
                <CardHeader>
                  <CardTitle>Stream Settings</CardTitle>
                  <CardDescription>Connect OBS or other streaming software</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <Label className="block mb-1">RTMP URL</Label>
                      <div className="flex items-center gap-2">
                        <Input
                          value={channel.rmtpUrl || "rtmp://stream.example.com/live"}
                          readOnly
                        />
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => copyToClipboard(
                            channel.rmtpUrl || "rtmp://stream.example.com/live",
                            "RTMP URL copied"
                          )}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    <div>
                      <Label className="block mb-1">Stream Key</Label>
                      <div className="flex items-center gap-2">
                        <Input
                          value={channel.rmtpSecret || "sk_live_1a2b3c4d5e6f"}
                          type="password"
                          readOnly
                        />
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => copyToClipboard(
                            channel.rmtpSecret || "sk_live_1a2b3c4d5e6f",
                            "Stream key copied"
                          )}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        Keep your stream key private. Do not share it with anyone.
                      </p>
                    </div>

                    <div className="mt-6 pt-4 border-t">
                      <h3 className="font-medium mb-2">OBS Configuration</h3>
                      <ol className="list-decimal list-inside space-y-2 text-sm">
                        <li>Open OBS Studio</li>
                        <li>Go to Settings &gt; Stream</li>
                        <li>Select Custom for Service</li>
                        <li>Enter the RTMP URL in the Server field</li>
                        <li>Enter your Stream Key in the Stream Key field</li>
                        <li>Click Apply and OK</li>
                      </ol>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          )}
        </Tabs>
      </div>
    </div>
  );
}