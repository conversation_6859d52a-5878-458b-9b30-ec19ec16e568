import apiClient from '../lib/api-client';
import { Event, CreateEventDto, EventPurchase } from '../interfaces/types';
import { uploadImage } from './file-service';
import { convertNeo4jIntegers } from '@/utils/neo4j-utils';
import { checkUserLiked } from './like-service';

export const fetchEvents = async (limit = 20, offset = 0) => {
  console.log(`Fetching events with limit=${limit}, offset=${offset}`);
  const response = await apiClient.get<Event[]>(`/events?limit=${limit}&offset=${offset}`);

  // Log the events received from the API
  console.log('Events received from API:', response.data);

  // Log each event's status and isLiked property
  response.data.forEach(event => {
    console.log(`Event ${event.id} (${event.title}) - Status: ${event.status}, isLiked: ${event.isLiked}`);
  });

  // Convert Neo4j integers and normalize event status values
  const normalizedEvents = response.data.map(event => {
    // First convert any Neo4j integers to JavaScript numbers
    const convertedEvent = convertNeo4jIntegers(event);

    // Then normalize the status
    if (convertedEvent.status) {
      // Convert status to lowercase for consistency
      const status = String(convertedEvent.status).toLowerCase();

      // Map status to one of the expected values
      if (status === 'draft' || status === 'published' || status === 'live' || status === 'ended' || status === 'upcoming') {
        convertedEvent.status = status as any;
      } else {
        // Default to 'draft' if status is not recognized
        console.log(`Converting unknown status '${status}' to 'draft' for event ${convertedEvent.id}`);
        convertedEvent.status = 'draft' as any;
      }
    } else {
      // Default to 'draft' if status is missing
      console.log(`Adding missing status as 'draft' for event ${convertedEvent.id}`);
      convertedEvent.status = 'draft' as any;
    }

    return convertedEvent;
  });

  // Log normalized events
  console.log('Normalized events:');
  normalizedEvents.forEach(event => {
    console.log(`Normalized event ${event.id} (${event.title}) - Status: ${event.status}, isLiked: ${event.isLiked}`);
  });

  // Check if the user is authenticated
  const token = localStorage.getItem('token');
  if (token) {
    console.log('User is authenticated, checking liked status for each event');

    // Check liked status for each event
    const likedStatusPromises = normalizedEvents.map(async (event) => {
      try {
        const likeStatus = await checkUserLiked(event.id);
        return { eventId: event.id, liked: likeStatus.liked };
      } catch (error) {
        console.error(`Error checking liked status for event ${event.id}:`, error);
        return { eventId: event.id, liked: false };
      }
    });

    // Wait for all liked status checks to complete
    const likedStatuses = await Promise.all(likedStatusPromises);

    // Update the events with the liked status
    normalizedEvents.forEach(event => {
      const likeStatus = likedStatuses.find(status => status.eventId === event.id);
      if (likeStatus) {
        event.isLiked = likeStatus.liked;
      }
    });

    console.log('Events after checking liked status:');
    normalizedEvents.forEach(event => {
      console.log(`Event ${event.id} (${event.title}) - Status: ${event.status}, isLiked: ${event.isLiked}`);
    });
  } else {
    console.log('User is not authenticated, skipping liked status check');
  }

  return normalizedEvents;
};

/**
 * Fetch homepage events (published, live, ended)
 * This uses the backend filtering for security reasons
 */
export const fetchHomepageEvents = async (limit = 20, offset = 0) => {
  console.log(`Fetching homepage events with limit=${limit}, offset=${offset}`);
  const response = await apiClient.get<Event[]>(`/events/homepage?limit=${limit}&offset=${offset}`);

  // Log the events received from the API
  console.log('Homepage events received from API:', response.data);

  // Log each event's status and isLiked property
  response.data.forEach(event => {
    console.log(`Homepage event ${event.id} (${event.title}) - Status: ${event.status}, isLiked: ${event.isLiked}`);
  });

  // Convert Neo4j integers and normalize event status values
  const normalizedEvents = response.data.map(event => {
    // First convert any Neo4j integers to JavaScript numbers
    const convertedEvent = convertNeo4jIntegers(event);

    // Then normalize the status
    if (convertedEvent.status) {
      // Convert status to lowercase for consistency
      const status = String(convertedEvent.status).toLowerCase();

      // Map status to one of the expected values
      if (status === 'draft' || status === 'published' || status === 'live' || status === 'ended' || status === 'upcoming') {
        convertedEvent.status = status as any;
      } else {
        // Default to 'draft' if status is not recognized
        console.log(`Converting unknown status '${status}' to 'draft' for event ${convertedEvent.id}`);
        convertedEvent.status = 'draft' as any;
      }
    } else {
      // Default to 'draft' if status is missing
      console.log(`Adding missing status as 'draft' for event ${convertedEvent.id}`);
      convertedEvent.status = 'draft' as any;
    }

    return convertedEvent;
  });

  // Log normalized events
  console.log('Normalized homepage events:');
  normalizedEvents.forEach(event => {
    console.log(`Normalized homepage event ${event.id} (${event.title}) - Status: ${event.status}, isLiked: ${event.isLiked}`);
  });

  return normalizedEvents;
};

export const fetchEvent = async (id: string) => {
  console.log('Fetching event with ID:', id);

  const response = await apiClient.get<Event>(`/events/${id}`);

  // Log the response data for debugging
  console.log('Fetched event data:', JSON.stringify(response.data, null, 2));
  console.log('Response data type:', typeof response.data);
  console.log('Response data is array:', Array.isArray(response.data));

  // Check if response.data is actually an event object
  if (!response.data || typeof response.data !== 'object' || typeof response.data === 'string') {
    console.error('Invalid event data received:', response.data);
    throw new Error(`Invalid event data received: ${JSON.stringify(response.data)}`);
  }

  // First convert any Neo4j integers to JavaScript numbers
  const convertedEvent = convertNeo4jIntegers(response.data);

  // Verify convertedEvent is still an object after conversion
  if (!convertedEvent || typeof convertedEvent !== 'object' || typeof convertedEvent === 'string') {
    console.error('convertNeo4jIntegers returned invalid data:', convertedEvent);
    throw new Error(`convertNeo4jIntegers returned invalid data: ${JSON.stringify(convertedEvent)}`);
  }

  // Then normalize the status
  if (convertedEvent.status) {
    // Convert status to lowercase for consistency
    const status = String(convertedEvent.status).toLowerCase();

    // Map status to one of the expected values
    if (status === 'draft' || status === 'published' || status === 'live' || status === 'ended' || status === 'upcoming') {
      convertedEvent.status = status as any;
    } else {
      // Default to 'draft' if status is not recognized
      console.log(`Converting unknown status '${status}' to 'draft' for event ${convertedEvent.id}`);
      convertedEvent.status = 'draft' as any;
    }
  } else {
    // Default to 'draft' if status is missing
    console.log(`Adding missing status as 'draft' for event ${convertedEvent.id}`);
    convertedEvent.status = 'draft' as any;
  }

  console.log('Normalized event status:', convertedEvent.status);

  // Check if the user is authenticated
  const token = localStorage.getItem('token');
  if (token) {
    console.log(`User is authenticated, checking liked status for event ${id}`);

    try {
      // Check if the user has liked this event
      const likeStatus = await checkUserLiked(id);
      convertedEvent.isLiked = likeStatus.liked;
      console.log(`Event ${id} liked status:`, likeStatus.liked);
    } catch (error) {
      console.error(`Error checking liked status for event ${id}:`, error);
      convertedEvent.isLiked = false;
    }
  } else {
    console.log('User is not authenticated, skipping liked status check');
    convertedEvent.isLiked = false;
  }

  return convertedEvent;
};

export const createEvent = async (channelId: string, data: CreateEventDto) => {
  const eventData = { ...data };

  // Regular JSON request
  const response = await apiClient.post<Event>(`/events/channel/${channelId}`, eventData);
  return convertNeo4jIntegers(response.data);
};

export const updateEvent = async (id: string, data: Partial<CreateEventDto>) => {
  const eventData = { ...data };

  // Remove the thumbnail property as it's not needed in the API request
  // Thumbnail updates are now handled by updateEventThumbnail
  if ('thumbnail' in eventData) {
    delete eventData.thumbnail;
  }

  // Log the data being sent to the API for debugging
  console.log('Updating event with data:', JSON.stringify(eventData, null, 2));

  // Regular JSON request
  const response = await apiClient.put<Event>(`/events/${id}`, eventData);

  // Log the response data for debugging
  console.log('Event update response:', JSON.stringify(response.data, null, 2));

  // Convert Neo4j integers to JavaScript numbers
  return convertNeo4jIntegers(response.data);
};

export const updateEventThumbnail = async (id: string, thumbnailFile: File) => {
  try {
    const formData = new FormData();
    formData.append('thumbnail', thumbnailFile);

    const response = await apiClient.put<Event>(`/events/${id}/thumbnail`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return convertNeo4jIntegers(response.data);
  } catch (error) {
    console.error('Failed to update event thumbnail:', error);
    throw new Error('Failed to update event thumbnail');
  }
};

export const fetchChannelEvents = async (channelId: string, limit = 20, offset = 0) => {
  const response = await apiClient.get<Event[]>(`/events/channel/${channelId}?limit=${limit}&offset=${offset}`);

  // Convert Neo4j integers to JavaScript numbers
  const normalizedEvents = response.data.map(event => convertNeo4jIntegers(event));

  // Check if the user is authenticated
  const token = localStorage.getItem('token');
  if (token) {
    console.log('User is authenticated, checking liked status for channel events');

    // Check liked status for each event
    const likedStatusPromises = normalizedEvents.map(async (event) => {
      try {
        const likeStatus = await checkUserLiked(event.id);
        return { eventId: event.id, liked: likeStatus.liked };
      } catch (error) {
        console.error(`Error checking liked status for event ${event.id}:`, error);
        return { eventId: event.id, liked: false };
      }
    });

    // Wait for all liked status checks to complete
    const likedStatuses = await Promise.all(likedStatusPromises);

    // Update the events with the liked status
    normalizedEvents.forEach(event => {
      const likeStatus = likedStatuses.find(status => status.eventId === event.id);
      if (likeStatus) {
        event.isLiked = likeStatus.liked;
      }
    });

    console.log('Channel events after checking liked status:');
    normalizedEvents.forEach(event => {
      console.log(`Event ${event.id} (${event.title}) - isLiked: ${event.isLiked}`);
    });
  } else {
    console.log('User is not authenticated, skipping liked status check for channel events');
  }

  return normalizedEvents;
};

/**
 * Fetch only published, live, and ended events for a channel
 * This uses the backend filtering for security reasons
 */
export const fetchPublicChannelEvents = async (channelId: string, limit = 20, offset = 0) => {
  console.log(`Fetching public channel events with limit=${limit}, offset=${offset}, channelId=${channelId}`);
  const response = await apiClient.get<{
    events: Event[];
    total: number;
    limit: number;
    offset: number;
  }>(`/events/channel-public/${channelId}?limit=${limit}&offset=${offset}`);

  // Log the response received from the API
  console.log('Public channel events response from API:', response.data);

  // Convert Neo4j integers to JavaScript numbers for events
  const normalizedEvents = response.data.events.map(event => convertNeo4jIntegers(event));

  // Check if the user is authenticated
  const token = localStorage.getItem('token');
  if (token) {
    console.log('User is authenticated, checking liked status for public channel events');

    // Check liked status for each event
    const likedStatusPromises = normalizedEvents.map(async (event) => {
      try {
        const likeStatus = await checkUserLiked(event.id);
        return { eventId: event.id, liked: likeStatus.liked };
      } catch (error) {
        console.error(`Error checking liked status for event ${event.id}:`, error);
        return { eventId: event.id, liked: false };
      }
    });

    // Wait for all liked status checks to complete
    const likedStatuses = await Promise.all(likedStatusPromises);

    // Update the events with the liked status
    normalizedEvents.forEach(event => {
      const likeStatus = likedStatuses.find(status => status.eventId === event.id);
      if (likeStatus) {
        event.isLiked = likeStatus.liked;
      }
    });

    console.log('Public channel events after checking liked status:');
    normalizedEvents.forEach(event => {
      console.log(`Event ${event.id} (${event.title}) - Status: ${event.status}, isLiked: ${event.isLiked}`);
    });
  } else {
    console.log('User is not authenticated, skipping liked status check for public channel events');
  }

  return {
    events: normalizedEvents,
    total: response.data.total,
    limit: response.data.limit,
    offset: response.data.offset
  };
};

export const checkEventAccess = async (id: string) => {
  const response = await apiClient.get(`/events/${id}/access`);
  return response.data.hasAccess;
};

export const setEventLive = async (id: string) => {
  const response = await apiClient.put<Event>(`/events/${id}/live`);
  return convertNeo4jIntegers(response.data);
};

export const endEvent = async (id: string) => {
  const response = await apiClient.put<Event>(`/events/${id}/end`);
  return convertNeo4jIntegers(response.data);
};

export const publishEvent = async (id: string) => {
  const response = await apiClient.put<Event>(`/events/${id}/publish`);
  return convertNeo4jIntegers(response.data);
};

export const getStreamStatus = async (id: string) => {
  const response = await apiClient.get(`/events/${id}/stream-status`);
  return response.data;
};

export const purchaseEvent = async (id: string, paymentIntentId: string) => {
  const response = await apiClient.post<EventPurchase>(`/events/${id}/purchase`, { paymentIntentId });
  return convertNeo4jIntegers(response.data);
};

export const fetchPurchasedEvents = async () => {
  const response = await apiClient.get<EventPurchase[]>('/events/purchases');
  return response.data.map(purchase => convertNeo4jIntegers(purchase));
};

export const deleteEvent = async (id: string) => {
  await apiClient.delete(`/events/${id}`);
};

export const getPlaybackToken = async (id: string, durationMinutes?: number) => {
  const body = durationMinutes ? { durationMinutes } : {};
  const response = await apiClient.post(`/events/${id}/playback-token`, body);
  return response.data;
};