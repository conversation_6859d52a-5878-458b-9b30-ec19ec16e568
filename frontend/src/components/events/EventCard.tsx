"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>ooter, CardHeader } from "@/components/ui/card";
import { Event } from "@/interfaces/types";
import { HeartIcon, CalendarIcon, MapPinIcon, EyeIcon, Activity, Clock, Play, CheckCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { formatRelativeDate, parseNeo4jDateTime, getEventStatusMessage, isEventEnded, isEventLive, isEventUpcoming } from "@/utils/date-utils";
import { convertNeo4jIntegers, isNeo4jInteger } from "@/utils/neo4j-utils";
import { useRouter } from "next/navigation";
import { useAppContext } from "@/context/NextAppContext";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface EventCardProps {
  event: Event;
  onClick?: () => void;
  hideSubscribeButton?: boolean;
  onWatch?: () => void;
  onLike?: () => void;
  isLiked?: boolean;
}

export function EventCard({
  event: rawEvent,
  onClick,
  hideSubscribeButton,
  onWatch,
  onLike,
  isLiked: propIsLiked
}: EventCardProps) {
  const router = useRouter();
  const { user } = useAppContext();

  // Convert all Neo4j integers in the event object to JavaScript numbers
  const event = convertNeo4jIntegers(rawEvent) as Event;

  // Determine if the event is liked
  // Priority: 1. Prop from parent component, 2. Event's isLiked property
  const isLiked = propIsLiked !== undefined ? propIsLiked : (event.isLiked === true);

  // Log the like status for debugging
  console.log(`EventCard - Event ${event.id} (${event.title}) - isLiked prop: ${propIsLiked}, event.isLiked: ${event.isLiked}, final isLiked: ${isLiked}`);

  // Get comprehensive event status using the same logic as watch page
  const eventStatusInfo = event.startTime && event.endTime
    ? getEventStatusMessage(event.startTime, event.endTime)
    : null;

  const eventIsLive = event.startTime && event.endTime && isEventLive(event.startTime, event.endTime);
  const eventIsEnded = event.startTime && event.endTime && isEventEnded(event.startTime, event.endTime);
  const eventIsUpcoming = event.startTime && event.endTime && isEventUpcoming(event.startTime, event.endTime);

  // Fallback to old logic for events without proper start/end times
  const eventDate = parseNeo4jDateTime(event.date || event.startTime);
  const now = new Date();
  const eventStart = eventDate ? new Date(eventDate) : null;
  const eventEnd = eventDate ? new Date(eventDate) : null;
  if (eventEnd) eventEnd.setHours(eventEnd.getHours() + 2);
  const fallbackIsLive = eventStart && eventEnd ? now >= eventStart && now <= eventEnd : false;
  const fallbackIsPastEvent = eventEnd ? now > eventEnd : false;
  const formattedDate = formatRelativeDate(event.date || event.startTime);

  // Format the address from videoAddress object or use the address string
  const formattedAddress = event.videoAddress
    ? `${event.videoAddress.streetNumber || ''} ${event.videoAddress.streetName || ''}, ${event.videoAddress.province || ''}, ${event.videoAddress.countryName || event.videoAddress.countryIso2 || ''}`
    : (event.address || 'Location not specified');

  const handleCardClick = (e: React.MouseEvent) => {
    if (onClick) {
      onClick();
    } else {
      onWatch?.();

      // If the event is published or live, route to the watch page
      // Otherwise, go to the event detail page
      if (event.status === 'published' || event.status === 'live') {
        router.push(`/event/watch/${String(event.id)}`);
      } else {
        router.push(`/event/${String(event.id)}`);
      }
    }
  };

  const handleButtonClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  const handleLikeClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onLike?.();
  };

  const thumbnailUrl = typeof event.thumbnail === 'string'
    ? event.thumbnail
    : (event.imageUrl || 'https://images.unsplash.com/photo-1605810230434-7631ac76ec81?q=80&w=600');

  return (
    <Card className="overflow-hidden h-full flex flex-col cursor-pointer" onClick={handleCardClick}>
      <div className="relative h-48">
        <img
          src={thumbnailUrl}
          alt={event.title}
          className="w-full h-full object-cover"
        />
        {(() => {
          // Priority: Use time-based status if available, otherwise fall back to event.status
          if (eventStatusInfo) {
            const getStatusIcon = () => {
              switch (eventStatusInfo.status) {
                case 'live':
                  return <Play className="h-3 w-3 mr-1" />;
                case 'ended':
                  return <CheckCircle className="h-3 w-3 mr-1" />;
                case 'upcoming':
                  return <Clock className="h-3 w-3 mr-1" />;
                default:
                  return <Clock className="h-3 w-3 mr-1" />;
              }
            };

            const getBadgeClassName = () => {
              switch (eventStatusInfo.status) {
                case 'live':
                  return "absolute top-2 right-2 bg-red-500 hover:bg-red-600 text-white flex items-center";
                case 'ended':
                  return "absolute top-2 right-2 bg-gray-500 hover:bg-gray-600 text-white flex items-center";
                case 'upcoming':
                  return "absolute top-2 right-2 bg-blue-500 hover:bg-blue-600 text-white flex items-center";
                default:
                  return "absolute top-2 right-2 flex items-center";
              }
            };

            return (
              <Badge
                variant="default"
                className={getBadgeClassName()}
              >
                {getStatusIcon()}
                {eventStatusInfo.status.toUpperCase()}
              </Badge>
            );
          }

          // Fallback to original status logic
          if (event.status === 'live' || fallbackIsLive) {
            return (
              <Badge
                variant="default"
                className="absolute top-2 right-2 bg-red-500 hover:bg-red-600 flex items-center gap-1"
              >
                <Activity className="h-3 w-3" />
                LIVE
              </Badge>
            );
          } else if (event.status === 'draft') {
            return (
              <Badge variant="outline" className="absolute top-2 right-2 bg-gray-100 text-gray-800">
                Draft
              </Badge>
            );
          } else if (event.status === 'published') {
            return (
              <Badge variant="outline" className="absolute top-2 right-2 bg-blue-100 text-blue-800">
                Published
              </Badge>
            );
          } else if (fallbackIsPastEvent) {
            return (
              <Badge variant="secondary" className="absolute top-2 right-2">
                Ended
              </Badge>
            );
          }

          return null;
        })()}
      </div>
      <div
        className="flex items-center gap-2 px-4 pt-3 cursor-pointer hover:text-primary"
        onClick={(e) => {
          e.stopPropagation();
          if (event.channel?.id) {
            router.push(`/channel-view/${String(event.channel.id)}`);
          }
        }}
      >
        <Avatar className="h-6 w-6">
          <AvatarImage
            src={event.channel?.imageUrl || '/placeholder-channel.svg'}
            alt={event.channel?.name || 'Channel'}
          />
          <AvatarFallback>{event.channel?.name?.charAt(0) || 'C'}</AvatarFallback>
        </Avatar>
        <span className="text-xs text-muted-foreground line-clamp-1 hover:text-primary">
          {event.channel?.name || 'Unknown Channel'}
        </span>
      </div>
      <CardHeader className="p-4 pt-2">
        <h3 className="text-lg font-bold line-clamp-1">{event.title}</h3>
        {/* Event Status Message */}
        {eventStatusInfo && (
          <div className="mt-1">
            <Badge
              variant={eventStatusInfo.variant}
              className="text-xs flex items-center w-fit"
            >
              {(() => {
                switch (eventStatusInfo.status) {
                  case 'live':
                    return <Play className="h-3 w-3 mr-1" />;
                  case 'ended':
                    return <CheckCircle className="h-3 w-3 mr-1" />;
                  case 'upcoming':
                    return <Clock className="h-3 w-3 mr-1" />;
                  default:
                    return <Clock className="h-3 w-3 mr-1" />;
                }
              })()}
              {eventStatusInfo.message}
            </Badge>
          </div>
        )}
      </CardHeader>
      <CardContent className="p-4 pt-0 flex-grow">
        <p className="text-muted-foreground text-sm line-clamp-2 mb-3">
          {event.description}
        </p>
        <div className="flex items-center text-sm text-muted-foreground mb-1">
          <CalendarIcon className="h-4 w-4 mr-1" />
          <span>{formattedDate}</span>
        </div>
        <div className="flex items-center text-sm text-muted-foreground">
          <MapPinIcon className="h-4 w-4 mr-1" />
          <span className="line-clamp-1">{formattedAddress}</span>
        </div>
      </CardContent>
      <CardFooter className="p-4 pt-0" onClick={handleButtonClick}>
        <div className="flex justify-between items-center w-full">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-1">
              <EyeIcon className="h-4 w-4 text-muted-foreground" />
              <span className="text-xs text-muted-foreground">{event.views || 0}</span>
            </div>
            <div
              className={cn(
                "flex items-center gap-1 transition-colors",
                eventIsEnded ? "cursor-not-allowed opacity-50" : "cursor-pointer hover:text-primary"
              )}
              onClick={eventIsEnded ? undefined : handleLikeClick}
            >
              <HeartIcon
                className={cn(
                  "h-4 w-4",
                  isLiked ? "fill-red-500 text-red-500" : "text-muted-foreground"
                )}
              />
              <span className={cn(
                "text-xs",
                isLiked ? "text-red-500 font-medium" : "text-muted-foreground"
              )}>
                {event.likes || 0}
              </span>
            </div>
          </div>
          {user && (
            <div className="flex items-center gap-2">
              <button
                onClick={eventIsEnded ? undefined : handleLikeClick}
                disabled={eventIsEnded}
                className={cn(
                  "p-2 rounded-full transition-colors",
                  eventIsEnded ? "cursor-not-allowed opacity-50" : "hover:bg-muted",
                  isLiked && "text-primary"
                )}
              >
                <HeartIcon className={cn("h-4 w-4", isLiked && "fill-primary")} />
              </button>
            </div>
          )}
        </div>
      </CardFooter>
    </Card>
  );
}
